<?php

namespace App\Http\Controllers\Vehicle;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Cloudinary\Cloudinary as CloudinarySDK;
use Cloudinary\Configuration\Configuration;

class VehicleController extends Controller
{


    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 2); // Default 2 vehicles per page
        $vehicles = DB::table('vehicles')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return Inertia::render('vehicles', [
            'vehicles' => $vehicles,
        ]);
    }

    public function store(Request $request): JsonResponse
    {


        $request->validate([
            'name' => 'required|string',
            'model' => 'required|string',
            'year' => 'required|integer',
            'image' => 'required|image',
            'owner' => 'nullable|string',
        ]);

        // Check file upload
        if (!$request->hasFile('image')) {
            return response()->json(['error' => 'No image uploaded'], 400);
        }
        $image = $request->file('image');
        if (!$image->isValid()) {
            return response()->json(['error' => 'Invalid image upload'], 400);
        }

        // Upload image to Cloudinary with error handling
        $imageUrl = null;
        try {
            Log::info('Starting direct Cloudinary upload...');

            // Initialize Cloudinary directly
            $cloudinary = new CloudinarySDK([
                'cloud' => [
                    'cloud_name' => env('CLOUDINARY_CLOUD_NAME'),
                    'api_key' => env('CLOUDINARY_API_KEY'),
                    'api_secret' => env('CLOUDINARY_API_SECRET'),
                ]
            ]);

            $result = $cloudinary->uploadApi()->upload($image->getRealPath(), [
                'folder' => 'vehicles',
                'transformation' => [
                    'width' => 800,
                    'height' => 600,
                    'crop' => 'fill',
                    'gravity' => 'center',
                    'quality' => 'auto',
                    'format' => 'auto'
                ]
            ]);

            Log::info('Direct Cloudinary upload result:', ['result' => $result]);

            if (isset($result['secure_url'])) {
                $imageUrl = $result['secure_url'];
            } else {
                throw new \Exception("No secure_url in Cloudinary response");
            }
        } catch (\Exception $e) {
            Log::error('Cloudinary upload failed: ' . $e->getMessage());
            return response()->json([
                'error' => 'Cloudinary upload failed',
                'details' => $e->getMessage(),
            ], 500);
        }


        $vehicle = [
            'name' => $request->input('name'),
            'model' => $request->input('model'),
            'year' => $request->input('year'),
            'image_url' => $imageUrl,
            'owner' => $request->input('owner'),
            'created_at' => now(),
            'updated_at' => now(),
        ];
        DB::table('vehicles')->insert($vehicle);

        return response()->json(['message' => 'Vehicle added successfully', 'vehicle' => $vehicle], 201);
    }
}
