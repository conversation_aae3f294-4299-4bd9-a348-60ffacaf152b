[2025-07-17 12:22:41] local.INFO: Starting Cloudinary upload...  
[2025-07-17 12:22:41] local.ERROR: Cloudinary upload failed: Trying to access array offset on value of type null {"trace":"#0 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\northTrippers\\vendor\\cloudinary-labs\\cloudinary-laravel\\src\\CloudinaryServiceProvider.php(64): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\Users\\Cybertron\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): CloudinaryLabs\\CloudinaryLaravel\\CloudinaryServiceProvider->CloudinaryLabs\\CloudinaryLaravel\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Cloudinary\\\\Clou...', Array, true)
#5 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Cloudinary\\\\Clou...', Array)
#6 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Cloudinary\\\\Clou...', Array)
#7 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1653): Illuminate\\Foundation\\Application->make('Cloudinary\\\\Clou...')
#8 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('Cloudinary\\\\Clou...')
#9 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('Cloudinary\\\\Clou...')
#10 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#11 C:\\Users\\<USER>\\northTrippers\\app\\Http\\Controllers\\Vehicle\\VehicleController.php(58): Illuminate\\Support\\Facades\\Facade::__callStatic('upload', Array)
#12 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Vehicle\\VehicleController->store(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Vehicle\\VehicleController), 'store')
#14 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\northTrippers\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\northTrippers\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\northTrippers\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-17 12:23:55] local.ERROR: Cannot use Cloudinary\Cloudinary as Cloudinary because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot use Cloudinary\\Cloudinary as Cloudinary because the name is already in use at C:\\Users\\<USER>\\northTrippers\\app\\Http\\Controllers\\Vehicle\\VehicleController.php:12)
[stacktrace]
#0 {main}
"} 
[2025-07-17 12:24:35] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-17 12:24:58] local.INFO: Starting direct Cloudinary upload...  
[2025-07-17 12:25:00] local.INFO: Direct Cloudinary upload result: {"result":{"Cloudinary\\Api\\ApiResponse":{"asset_id":"ceb0be2d14d0f70af4944fa626bf3c8a","public_id":"vehicles/tucxyqmbdirki6ldbvb7","version":1752755099,"version_id":"ffff7fa2bb709c41aad820191c747199","signature":"90df3cd9305c325193c4af109227ce6f2ddc40e6","width":81,"height":109,"format":"png","resource_type":"image","created_at":"2025-07-17T12:24:59Z","tags":[],"bytes":13075,"type":"upload","etag":"ed173319b26f1c4548b37096cab9c041","placeholder":false,"url":"http://res.cloudinary.com/djirapycg/image/upload/v1752755099/vehicles/tucxyqmbdirki6ldbvb7.png","secure_url":"https://res.cloudinary.com/djirapycg/image/upload/v1752755099/vehicles/tucxyqmbdirki6ldbvb7.png","asset_folder":"vehicles","display_name":"tucxyqmbdirki6ldbvb7","original_filename":"php8D26","original_extension":"tmp","api_key":"965556686482922"}}} 
[2025-07-17 12:25:38] local.INFO: Starting direct Cloudinary upload...  
[2025-07-17 12:25:41] local.INFO: Direct Cloudinary upload result: {"result":{"Cloudinary\\Api\\ApiResponse":{"asset_id":"412f04076a6269003f00c2c6a8a20ac6","public_id":"vehicles/okzwxqbjviif3bun68ti","version":1752755140,"version_id":"72f8ece22ec35355e985f2352b9dc7ee","signature":"5be97917d8be78d9ee27ecf3361fcc9ba9a1b13c","width":1936,"height":1492,"format":"jpg","resource_type":"image","created_at":"2025-07-17T12:25:40Z","tags":[],"bytes":332654,"type":"upload","etag":"0d51628387b20e7ad03d08fe1802ab66","placeholder":false,"url":"http://res.cloudinary.com/djirapycg/image/upload/v1752755140/vehicles/okzwxqbjviif3bun68ti.jpg","secure_url":"https://res.cloudinary.com/djirapycg/image/upload/v1752755140/vehicles/okzwxqbjviif3bun68ti.jpg","asset_folder":"vehicles","display_name":"okzwxqbjviif3bun68ti","original_filename":"php28BB","original_extension":"tmp","api_key":"965556686482922"}}} 
[2025-07-17 12:26:05] local.INFO: Starting direct Cloudinary upload...  
[2025-07-17 12:26:09] local.INFO: Direct Cloudinary upload result: {"result":{"Cloudinary\\Api\\ApiResponse":{"asset_id":"ebd1e5b49910971aa6a160baf9b727db","public_id":"vehicles/uzym2xuuslnex7o3erxu","version":1752755168,"version_id":"25adc98352ba9f924d0c54f0bbba8cf5","signature":"150a4d70e4c8e26ab3cbc92278b86d3a7a7b3e6f","width":1936,"height":1492,"format":"jpg","resource_type":"image","created_at":"2025-07-17T12:26:08Z","tags":[],"bytes":332654,"type":"upload","etag":"0d51628387b20e7ad03d08fe1802ab66","placeholder":false,"url":"http://res.cloudinary.com/djirapycg/image/upload/v1752755168/vehicles/uzym2xuuslnex7o3erxu.jpg","secure_url":"https://res.cloudinary.com/djirapycg/image/upload/v1752755168/vehicles/uzym2xuuslnex7o3erxu.jpg","asset_folder":"vehicles","display_name":"uzym2xuuslnex7o3erxu","original_filename":"php95AF","original_extension":"tmp","api_key":"965556686482922"}}} 
[2025-07-17 12:29:07] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-17 12:31:26] local.INFO: Starting direct Cloudinary upload...  
[2025-07-17 12:31:28] local.INFO: Direct Cloudinary upload result: {"result":{"Cloudinary\\Api\\ApiResponse":{"asset_id":"6ff5ecbed100cc510890f65a5f93db87","public_id":"vehicles/adsq27oubqqi3qkmgrch","version":1752755487,"version_id":"839d73fc46508483111cfb34e481f534","signature":"850927ffe95a40f239effccb7ee7ab6368ebf809","width":30,"height":30,"format":"png","resource_type":"image","created_at":"2025-07-17T12:31:27Z","tags":[],"bytes":547,"type":"upload","etag":"550d782e565ad1336584b2fd475286f1","placeholder":false,"url":"http://res.cloudinary.com/djirapycg/image/upload/v1752755487/vehicles/adsq27oubqqi3qkmgrch.png","secure_url":"https://res.cloudinary.com/djirapycg/image/upload/v1752755487/vehicles/adsq27oubqqi3qkmgrch.png","asset_folder":"vehicles","display_name":"adsq27oubqqi3qkmgrch","original_filename":"php78AA","original_extension":"tmp","api_key":"965556686482922"}}} 
[2025-07-17 12:34:25] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-17 12:36:13] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-17 12:39:28] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-17 12:39:41] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-17 12:39:46] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-17 12:41:02] local.INFO: Starting direct Cloudinary upload...  
[2025-07-17 12:41:05] local.INFO: Direct Cloudinary upload result: {"result":{"Cloudinary\\Api\\ApiResponse":{"asset_id":"7f0510316672718b9f01cdb5ffc8710c","public_id":"vehicles/ld9jen4nsuau7epbhdkc","version":1752756064,"version_id":"77a20f9e3b4c5dfc0a544e51cdf6c5ca","signature":"7bbc86d7f176e8311aa0cb79fdc956e3d98a1890","width":800,"height":600,"format":"png","resource_type":"image","created_at":"2025-07-17T12:41:04Z","tags":[],"bytes":100810,"type":"upload","etag":"3650c8a4b0f6e044a34ee2b6dd1b5629","placeholder":false,"url":"http://res.cloudinary.com/djirapycg/image/upload/v1752756064/vehicles/ld9jen4nsuau7epbhdkc.png","secure_url":"https://res.cloudinary.com/djirapycg/image/upload/v1752756064/vehicles/ld9jen4nsuau7epbhdkc.png","asset_folder":"vehicles","display_name":"ld9jen4nsuau7epbhdkc","original_filename":"php44FD","original_extension":"tmp","api_key":"965556686482922"}}} 
[2025-07-17 12:41:25] local.INFO: Starting direct Cloudinary upload...  
[2025-07-17 12:41:29] local.INFO: Direct Cloudinary upload result: {"result":{"Cloudinary\\Api\\ApiResponse":{"asset_id":"4743e2396633720f3e168938d7e13ae0","public_id":"vehicles/rcqfjwt2yrabah42rmmv","version":1752756088,"version_id":"807804cf8a30e55acae77a3eaa88abc6","signature":"7c9d64594cc00dacccffd398327b13ac77920ecb","width":800,"height":600,"format":"jpg","resource_type":"image","created_at":"2025-07-17T12:41:28Z","tags":[],"bytes":57527,"type":"upload","etag":"b6722bd6aea06dec067633d283975c0d","placeholder":false,"url":"http://res.cloudinary.com/djirapycg/image/upload/v1752756088/vehicles/rcqfjwt2yrabah42rmmv.jpg","secure_url":"https://res.cloudinary.com/djirapycg/image/upload/v1752756088/vehicles/rcqfjwt2yrabah42rmmv.jpg","asset_folder":"vehicles","display_name":"rcqfjwt2yrabah42rmmv","original_filename":"php9C65","original_extension":"tmp","api_key":"965556686482922"}}} 
[2025-07-17 12:46:21] local.INFO: Starting direct Cloudinary upload...  
[2025-07-17 12:46:24] local.INFO: Direct Cloudinary upload result: {"result":{"Cloudinary\\Api\\ApiResponse":{"asset_id":"70fc7f5d4cfc645c628949834cbb2c44","public_id":"vehicles/rbconigxxfyxgui4dww0","version":1752756383,"version_id":"4a4387151e12188795313c46525c1b9c","signature":"bfd0fcc38d5de5ba70b19c7c1d02817ed566afcd","width":800,"height":600,"format":"png","resource_type":"image","created_at":"2025-07-17T12:46:23Z","tags":[],"bytes":100810,"type":"upload","etag":"3650c8a4b0f6e044a34ee2b6dd1b5629","placeholder":false,"url":"http://res.cloudinary.com/djirapycg/image/upload/v1752756383/vehicles/rbconigxxfyxgui4dww0.png","secure_url":"https://res.cloudinary.com/djirapycg/image/upload/v1752756383/vehicles/rbconigxxfyxgui4dww0.png","asset_folder":"vehicles","display_name":"rbconigxxfyxgui4dww0","original_filename":"php21BF","original_extension":"tmp","api_key":"965556686482922"}}} 
[2025-07-17 12:48:32] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-17 12:51:20] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-17 12:52:57] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-17 12:55:02] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\northTrippers\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\northTrippers\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\northTrippers\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
