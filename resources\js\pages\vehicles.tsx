import { useState, useEffect, ChangeEvent, FormEvent } from 'react';
import { usePage, router } from '@inertiajs/react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Plus, Car, Calendar, User, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Toaster } from '@/components/ui/toaster';

interface Vehicle {
  id: number;
  name: string;
  model: string;
  year: number;
  image_url?: string;
  owner?: string;
}

interface PaginatedVehicles {
  data: Vehicle[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
  links: Array<{
    url: string | null;
    label: string;
    active: boolean;
  }>;
}

export default function Vehicles({ vehicles: propVehicles }: { vehicles?: PaginatedVehicles }) {
  const page = usePage<{ vehicles?: PaginatedVehicles }>();
  const vehiclesData = propVehicles || page.props.vehicles;
  const [vehicles, setVehicles] = useState<Vehicle[]>(vehiclesData?.data || []);
  const [paginationInfo, setPaginationInfo] = useState(vehiclesData || null);
  const [form, setForm] = useState({
    name: '',
    model: '',
    year: '',
    owner: '',
    image: null as File | null,
  });
  const [loading, setLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  const handlePageChange = async (page: number) => {
    if (!page || page === paginationInfo?.current_page) return;

    setIsLoading(true);

    try {
      const response = await fetch(`/vehicles/paginate?page=${page}&per_page=${paginationInfo?.per_page || 2}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
      });

      if (!response.ok) throw new Error('Failed to fetch vehicles');

      const data = await response.json();
      setVehicles(data.vehicles.data);
      setPaginationInfo(data.vehicles);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load vehicles",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (page.props.vehicles) {
      setVehicles(page.props.vehicles.data || []);
      setPaginationInfo(page.props.vehicles);
      setIsLoading(false);
    }
  }, [page.props.vehicles]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value, files } = e.target;
    if (name === 'image' && files) {
      setForm(f => ({ ...f, image: files[0] }));
    } else {
      setForm(f => ({ ...f, [name]: value }));
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);

    const formData = new FormData();
    formData.append('name', form.name);
    formData.append('model', form.model);
    formData.append('year', form.year);
    formData.append('owner', form.owner);
    if (form.image) formData.append('image', form.image);

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';

    try {
      const res = await fetch('/vehicles', {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-TOKEN': csrfToken,
        },
      });

      if (!res.ok) throw new Error('Failed to add vehicle');

      toast({
        title: "Success!",
        description: "Vehicle added successfully!",
        variant: "success",
        duration: 5000,
      });

      setForm({ name: '', model: '', year: '', owner: '', image: null });
      setIsDialogOpen(false);

      // Refresh current page data
      router.reload({ only: ['vehicles'] });

    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : 'Unknown error',
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  const VehicleSkeleton = () => (
    <Card className="overflow-hidden">
      <div className="aspect-video relative bg-muted">
        <Skeleton className="w-full h-full" />
      </div>
      <div className="p-4 space-y-3">
        <div>
          <Skeleton className="h-6 w-3/4 mb-2" />
          <Skeleton className="h-4 w-1/2" />
        </div>
        <div className="flex items-center gap-4">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-20" />
        </div>
      </div>
    </Card>
  );

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Vehicles</h1>
          <p className="text-muted-foreground">Manage your vehicle fleet</p>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                Add Vehicle
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Vehicle</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Vehicle Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={form.name}
                  onChange={handleChange}
                  placeholder="Enter vehicle name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Input
                  id="model"
                  name="model"
                  value={form.model}
                  onChange={handleChange}
                  placeholder="Enter model"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="year">Year</Label>
                <Input
                  id="year"
                  name="year"
                  type="number"
                  value={form.year}
                  onChange={handleChange}
                  placeholder="Enter year"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="owner">Owner (Optional)</Label>
                <Input
                  id="owner"
                  name="owner"
                  value={form.owner}
                  onChange={handleChange}
                  placeholder="Enter owner name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="image">Vehicle Image</Label>
                <Input
                  id="image"
                  name="image"
                  type="file"
                  accept="image/*"
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={loading} className="flex-1">
                  {loading ? 'Adding...' : 'Add Vehicle'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {isLoading ? (
          Array.from({ length: paginationInfo?.per_page || 2 }).map((_, i) => <VehicleSkeleton key={i} />)
        ) : vehicles.length > 0 ? (
          vehicles.map((vehicle) => (
            <Card key={vehicle.id} className="overflow-hidden hover:shadow-md transition-shadow">
              <div className="aspect-video relative bg-muted">
                {vehicle.image_url ? (
                  <img
                    src={vehicle.image_url}
                    alt={vehicle.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <Car className="h-12 w-12 text-muted-foreground" />
                  </div>
                )}
              </div>
              <div className="p-4 space-y-3">
                <div>
                  <h3 className="font-semibold text-lg leading-tight">{vehicle.name}</h3>
                  <p className="text-muted-foreground">{vehicle.model}</p>
                </div>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {vehicle.year}
                  </div>
                  {vehicle.owner && (
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      {vehicle.owner}
                    </div>
                  )}
                </div>
              </div>
            </Card>
          ))
        ) : (
          <div className="col-span-full text-center py-12">
            <Car className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No vehicles found</h3>
            <p className="text-muted-foreground mb-4">Get started by adding your first vehicle</p>
            <Button onClick={() => setIsDialogOpen(true)} className="gap-2">
              <Plus className="h-4 w-4" />
              Add Vehicle
            </Button>
          </div>
        )}
      </div>

      {/* Loading indicator for pagination */}
      {isLoading && (
        <div className="flex justify-center items-center py-4">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          <span className="ml-2 text-sm text-muted-foreground">Loading vehicles...</span>
        </div>
      )}

      {/* Pagination */}
      {paginationInfo && paginationInfo.last_page > 1 && (
        <div className="flex justify-center mt-8">
          <Pagination>
            <PaginationContent>
              {paginationInfo.current_page > 1 && (
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    size="default"
                    onClick={(e) => {
                      e.preventDefault();
                      if (!isLoading) handlePageChange(paginationInfo.current_page - 1);
                    }}
                    style={{ opacity: isLoading ? 0.5 : 1, pointerEvents: isLoading ? 'none' : 'auto' }}
                  />
                </PaginationItem>
              )}

              {paginationInfo.links
                .filter(link => link.label !== '&laquo; Previous' && link.label !== 'Next &raquo;')
                .map((link, index) => (
                  <PaginationItem key={index}>
                    <PaginationLink
                      href="#"
                      size="icon"
                      isActive={link.active}
                      onClick={(e) => {
                        e.preventDefault();
                        const pageNum = parseInt(link.label);
                        if (!isNaN(pageNum) && !isLoading) handlePageChange(pageNum);
                      }}
                      style={{ opacity: isLoading ? 0.5 : 1, pointerEvents: isLoading ? 'none' : 'auto' }}
                    >
                      {link.label}
                    </PaginationLink>
                  </PaginationItem>
                ))}

              {paginationInfo.current_page < paginationInfo.last_page && (
                <PaginationItem>
                  <PaginationNext
                    href="#"
                    size="default"
                    onClick={(e) => {
                      e.preventDefault();
                      if (!isLoading) handlePageChange(paginationInfo.current_page + 1);
                    }}
                    style={{ opacity: isLoading ? 0.5 : 1, pointerEvents: isLoading ? 'none' : 'auto' }}
                  />
                </PaginationItem>
              )}
            </PaginationContent>
          </Pagination>
        </div>
      )}
      <Toaster />
    </div>
  );
}


